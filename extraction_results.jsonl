{"extractions": [{"extraction_class": "genre", "extraction_text": "action-packed thriller", "char_interval": {"start_pos": 85, "end_pos": 107}, "alignment_status": "match_exact", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "action", "secondary_genre": "thriller"}}, {"extraction_class": "character", "extraction_text": "<PERSON>", "char_interval": {"start_pos": 0, "end_pos": 12}, "alignment_status": "match_fuzzy", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "detective"}}, {"extraction_class": "theme", "extraction_text": "fights terrorists", "char_interval": {"start_pos": 13, "end_pos": 30}, "alignment_status": "match_fuzzy", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "conflict", "setting": "urban"}}], "text": "<PERSON> fights terrorists in a Los Angeles skyscraper during Christmas Eve. The action-packed thriller features intense gunfights and explosive scenes.", "document_id": "doc_09fc271f"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "fantasy adventure", "char_interval": {"start_pos": 90, "end_pos": 107}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "fantasy", "secondary_genre": "action"}}, {"extraction_class": "character", "extraction_text": "young wizard named Harry Potter", "char_interval": {"start_pos": 2, "end_pos": 33}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "wizard"}}, {"extraction_class": "theme", "extraction_text": "magical abilities", "char_interval": {"start_pos": 48, "end_pos": 65}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "magic", "setting": "school"}}, {"extraction_class": "theme", "extraction_text": "epic battles", "char_interval": {"start_pos": 139, "end_pos": 151}, "alignment_status": "match_exact", "extraction_index": 4, "group_index": 3, "description": null, "attributes": {"theme_type": "conflict", "setting": "fantasy_world"}}], "text": "A young wizard named Harry Potter discovers his magical abilities at Hogwarts School. The fantasy adventure includes magical creatures and epic battles.", "document_id": "doc_e71a17f0"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "superhero movie", "char_interval": {"start_pos": 68, "end_pos": 83}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "superhero"}}, {"extraction_class": "character", "extraction_text": "Tony Stark", "char_interval": {"start_pos": 0, "end_pos": 10}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "hero"}}, {"extraction_class": "theme", "extraction_text": "cutting-edge technology", "char_interval": {"start_pos": 94, "end_pos": 117}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "technology"}}], "text": "Tony Stark builds an advanced suit of armor to become Iron Man. The superhero movie showcases cutting-edge technology and spectacular action sequences.", "document_id": "doc_e6590ac4"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "horror film", "char_interval": {"start_pos": 87, "end_pos": 98}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "horror"}}, {"extraction_class": "character", "extraction_text": "group of friends", "char_interval": {"start_pos": 2, "end_pos": 18}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "friends"}}, {"extraction_class": "theme", "extraction_text": "supernatural creatures", "char_interval": {"start_pos": 54, "end_pos": 76}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "survival", "setting": "forest"}}], "text": "A group of friends get lost in a haunted forest where supernatural creatures lurk. The horror film creates a terrifying atmosphere with jump scares.", "document_id": "doc_53e395a4"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "crime thriller", "char_interval": {"start_pos": 80, "end_pos": 94}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "crime", "secondary_genre": "thriller"}}, {"extraction_class": "character", "extraction_text": "Two detectives", "char_interval": {"start_pos": 0, "end_pos": 14}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "detective"}}, {"extraction_class": "theme", "extraction_text": "mysterious murders", "char_interval": {"start_pos": 39, "end_pos": 57}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "investigation", "setting": "urban"}}], "text": "Two detectives investigate a series of mysterious murders in New York City. The crime thriller features suspenseful plot twists and dramatic confrontations.", "document_id": "doc_e40eea1e"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "sci-fi thriller", "char_interval": {"start_pos": 83, "end_pos": 98}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "sci-fi", "secondary_genre": "thriller"}}, {"extraction_class": "character", "extraction_text": "brilliant scientist", "char_interval": {"start_pos": 2, "end_pos": 21}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "scientist"}}, {"extraction_class": "theme", "extraction_text": "artificial intelligence that becomes self-aware", "char_interval": {"start_pos": 30, "end_pos": 77}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "technology", "setting": "future"}}, {"extraction_class": "theme", "extraction_text": "human survival", "char_interval": {"start_pos": 147, "end_pos": 161}, "alignment_status": "match_exact", "extraction_index": 4, "group_index": 3, "description": null, "attributes": {"theme_type": "survival", "setting": "future"}}], "text": "A brilliant scientist creates artificial intelligence that becomes self-aware. The sci-fi thriller explores the dangers of advanced technology and human survival.", "document_id": "doc_6a2c9cc3"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "romantic comedy", "char_interval": {"start_pos": 2, "end_pos": 17}, "alignment_status": "match_exact", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "comedy", "secondary_genre": "drama"}}, {"extraction_class": "character", "extraction_text": "two friends", "char_interval": {"start_pos": 24, "end_pos": 35}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "friends"}}, {"extraction_class": "theme", "extraction_text": "personal growth", "char_interval": {"start_pos": 106, "end_pos": 121}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "personal_growth"}}, {"extraction_class": "theme", "extraction_text": "relationship dynamics", "char_interval": {"start_pos": 126, "end_pos": 147}, "alignment_status": "match_exact", "extraction_index": 4, "group_index": 3, "description": null, "attributes": {"theme_type": "romance"}}], "text": "A romantic comedy about two friends who fall in love during a cross-country road trip. The drama explores personal growth and relationship dynamics.", "document_id": "doc_1e2117bb"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "fantasy", "char_interval": {"start_pos": 128, "end_pos": 135}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "fantasy"}}, {"extraction_class": "character", "extraction_text": "evil sorcerer", "char_interval": {"start_pos": 3, "end_pos": 16}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "antagonist", "type": "villain"}}, {"extraction_class": "character", "extraction_text": "brave hero", "char_interval": {"start_pos": 61, "end_pos": 71}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"role": "protagonist", "type": "hero"}}, {"extraction_class": "theme", "extraction_text": "master ancient magic", "char_interval": {"start_pos": 95, "end_pos": 115}, "alignment_status": "match_exact", "extraction_index": 4, "group_index": 3, "description": null, "attributes": {"theme_type": "magic", "setting": "fantasy_world"}}, {"extraction_class": "theme", "extraction_text": "save the fantasy world", "char_interval": {"start_pos": 119, "end_pos": 141}, "alignment_status": "match_exact", "extraction_index": 5, "group_index": 4, "description": null, "attributes": {"theme_type": "survival", "setting": "fantasy_world"}}], "text": "An evil sorcerer threatens to destroy the magical kingdom. A brave hero must gather allies and master ancient magic to save the fantasy world.", "document_id": "doc_e48f3540"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "action sci-fi", "char_interval": {"start_pos": 61, "end_pos": 74}, "alignment_status": "match_exact", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "action", "secondary_genre": "sci-fi"}}, {"extraction_class": "character", "extraction_text": "Space marines", "char_interval": {"start_pos": 131, "end_pos": 136}, "alignment_status": "match_lesser", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "military"}}, {"extraction_class": "theme", "extraction_text": "battle alien invaders", "char_interval": {"start_pos": 14, "end_pos": 35}, "alignment_status": "match_fuzzy", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "conflict", "setting": "space"}}], "text": "Space marines battle alien invaders on a distant planet. The action sci-fi movie features futuristic weapons and intense combat in space.", "document_id": "doc_acce78dc"}
{"extractions": [{"extraction_class": "genre", "extraction_text": "horror thriller", "char_interval": {"start_pos": 70, "end_pos": 85}, "alignment_status": "match_fuzzy", "extraction_index": 1, "group_index": 0, "description": null, "attributes": {"primary_genre": "horror", "secondary_genre": "thriller"}}, {"extraction_class": "character", "extraction_text": "detective", "char_interval": {"start_pos": 2, "end_pos": 11}, "alignment_status": "match_exact", "extraction_index": 2, "group_index": 1, "description": null, "attributes": {"role": "protagonist", "type": "detective"}}, {"extraction_class": "theme", "extraction_text": "supernatural crimes", "char_interval": {"start_pos": 25, "end_pos": 44}, "alignment_status": "match_exact", "extraction_index": 3, "group_index": 2, "description": null, "attributes": {"theme_type": "investigation", "setting": "victorian"}}], "text": "A detective investigates supernatural crimes in Victorian London. The horror thriller combines period drama with paranormal investigation themes.", "document_id": "doc_a17de4e1"}
