import os
import langextract as lx
import textwrap
from google import genai
from google.genai.types import EmbedContentConfig
from pymilvus import MilvusClient, DataType
from langextract import factory

import uuid
# os.environ["GEMINI_API_KEY"] = "AIzaSyDA5AdyxeMpGxMm_J9rNhw4Q50XD0kZx44"

genai_client = genai.Client()
COLLECTION_NAME = "document_extractions"
EMBEDDING_MODEL = "gemini-embedding-001"
EMBEDDING_DIM = 3072 # Default dimension for gemini-embedding-0

# 使用内存模式而不是本地文件，避免 milvus-lite 依赖问题
client = MilvusClient(uri="http://192.168.10.132:19530")

sample_documents = [
    "<PERSON> fights terrorists in a Los Angeles skyscraper during Christmas Eve. The action-packed thriller features intense gunfights and explosive scenes.",
    "A young wizard named <PERSON> discovers his magical abilities at Hogwarts School. The fantasy adventure includes magical creatures and epic battles.",
    "<PERSON> builds an advanced suit of armor to become Iron Man. The superhero movie showcases cutting-edge technology and spectacular action sequences.",
    "A group of friends get lost in a haunted forest where supernatural creatures lurk. The horror film creates a terrifying atmosphere with jump scares.",
    "Two detectives investigate a series of mysterious murders in New York City. The crime thriller features suspenseful plot twists and dramatic confrontations.",
    "A brilliant scientist creates artificial intelligence that becomes self-aware. The sci-fi thriller explores the dangers of advanced technology and human survival.",
    "A romantic comedy about two friends who fall in love during a cross-country road trip. The drama explores personal growth and relationship dynamics.",
    "An evil sorcerer threatens to destroy the magical kingdom. A brave hero must gather allies and master ancient magic to save the fantasy world.",
    "Space marines battle alien invaders on a distant planet. The action sci-fi movie features futuristic weapons and intense combat in space.",
    "A detective investigates supernatural crimes in Victorian London. The horror thriller combines period drama with paranormal investigation themes.",
]
sample_documents_zh = [
    "约翰·麦克莱恩在圣诞前夜于洛杉矶摩天大楼中与恐怖分子作战。这部充满动作的惊悚片包含激烈的枪战和爆炸场景。",
    "一位名叫哈利·波特的年轻巫师在霍格沃茨学校发现了自己的魔法能力。这部奇幻冒险电影包括魔法生物和史诗般的战斗。",
    "托尼·斯塔克打造了一套先进的盔甲成为钢铁侠。这部超级英雄电影展示了尖端科技和壮观的动作场面。",
    "一群朋友在闹鬼的森林中迷路，那里潜伏着超自然生物。这部恐怖片营造了令人毛骨悚然的氛围和惊吓场景。",
    "两名侦探在纽约市调查一系列神秘谋杀案。这部犯罪惊悚片包含悬疑的剧情反转和戏剧性的对抗场面。",
    "一位天才科学家创造了自我意识的人工智能。这部科幻惊悚片探讨了先进科技的危险以及人类的生存问题。",
    "一部浪漫喜剧讲述两位朋友在跨国公路旅行中相爱。这部剧情片探讨了个人成长和人际关系动态。",
    "一位邪恶的巫师威胁要摧毁魔法王国。一位勇敢的英雄必须集合盟友并掌握古老魔法以拯救幻想世界。",
    "太空陆战队在遥远的星球上与外星入侵者作战。这部动作科幻电影展示了未来武器和太空中的激烈战斗。",
    "一名侦探在维多利亚时代的伦敦调查超自然犯罪。这部恐怖惊悚片结合了时期剧情和超自然调查主题。",
]


print("=== LangExtract + Milvus Integration Demo ===")
print(f"Preparing to process {len(sample_documents)} documents")


print("\n1. Setting up Milvus collection...")

# Drop existing collection if it exists
if client.has_collection(collection_name=COLLECTION_NAME):
    client.drop_collection(collection_name=COLLECTION_NAME)
    print(f"Dropped existing collection: {COLLECTION_NAME}")

# Create collection schema
schema = client.create_schema(
    auto_id=False,
    enable_dynamic_field=True,
    description="Document extraction results and vector storage",
)

# Add fields - simplified to 3 main metadata fields
schema.add_field(
    field_name="id", datatype=DataType.VARCHAR, max_length=100, is_primary=True
)
schema.add_field(field_name="document_text", datatype=DataType.VARCHAR, max_length=10000)

schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=EMBEDDING_DIM)

# Create collection
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)
print(f"Collection '{COLLECTION_NAME}' created successfully")

# Create vector index
index_params = client.prepare_index_params()
index_params.add_index(field_name="embedding",index_type="AUTOINDEX",metric_type="COSINE",)
client.create_index(collection_name=COLLECTION_NAME, index_params=index_params)
print("Vector index created successfully")

print("\n2. Extracting tags from documents...")
# Define extraction prompt - for movie descriptions, specify attribute value ranges
prompt = textwrap.dedent(
    """\
    Extract movie genre, main characters, and key themes from movie descriptions.
    Use exact text for extractions. Do not paraphrase or overlap entities.
    For each extraction, provide attributes with values from these predefined sets:
    Genre attributes:
    - primary_genre: ["action", "comedy", "drama", "horror", "sci-fi", "fantasy", "thriller", "crime", "superhero"]
    - secondary_genre: ["action", "comedy", "drama", "horror", "sci-fi", "fantasy", "thriller", "crime", "superhero"]
    Character attributes:
    - role: ["protagonist", "antagonist", "supporting"]
    - type: ["hero", "villain", "detective", "military", "wizard", "scientist", "friends", "investigator"]
    - theme_type: ["conflict", "investigation", "personal_growth", "technology", "magic", "survival", "romance"]
    - setting: ["urban", "space", "fantasy_world", "school", "forest", "victorian", "america", "future"]
    Focus on identifying key elements that would be useful for movie search and filtering."""
)

prompt_zh = textwrap.dedent(
    """\
    从电影描述中提取电影类型、主要角色和关键主题。
    使用原文中的确切文本进行提取。不要改写或重叠实体。
    对于每个提取项，提供的属性需使用以下预定义集合中的值：
    类型（Genre）属性：
    - primary_genre: ["action"（动作）, "comedy"（喜剧）, "drama"（剧情）, "horror"（恐怖）, "sci-fi"（科幻）, "fantasy"（奇幻）, "thriller"（惊悚）, "crime"（犯罪）, "superhero"（超级英雄）]
    - secondary_genre: ["action", "comedy", "drama", "horror", "sci-fi", "fantasy", "thriller", "crime", "superhero"]
    角色（Character）属性：
    - role: ["protagonist"（主角）, "antagonist"（反派）, "supporting"（配角）]
    - type: ["hero"（英雄）, "villain"（恶棍）, "detective"（侦探）, "military"（军人）, "wizard"（巫师）, "scientist"（科学家）, "friends"（朋友）, "investigator"（调查员）]
    - theme_type: ["conflict"（冲突）, "investigation"（调查）, "personal_growth"（个人成长）, "technology"（科技）, "magic"（魔法）, "survival"（生存）, "romance"（爱情）]
    - setting: ["urban"（城市）, "space"（太空）, "fantasy_world"（幻想世界）, "school"（学校）, "forest"（森林）, "victorian"（维多利亚时代）, "america"（美国）, "future"（未来）]
    重点在于识别对电影搜索和筛选有帮助的关键元素。"""
)

# Provide examples to guide the model - n-shot examples for movie descriptions
# Unify attribute keys to ensure consistency in extraction results
examples = [
    lx.data.ExampleData(
        text="A space marine battles alien creatures on a distant planet. The sci-fi action movie features futuristic weapons and intense combat scenes.",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="sci-fi action",
                attributes={"primary_genre": "sci-fi","secondary_genre": "action"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="space marine",
                attributes={"role": "protagonist","type": "military"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="battles alien creatures",
                attributes={"theme_type": "conflict","setting": "space"},
            ),
        ],
    ),
    lx.data.ExampleData(
        text="A detective investigates supernatural murders in Victorian London. The horror thriller film combines period drama with paranormal elements.",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="horror thriller",
                attributes={"primary_genre": "horror", "secondary_genre": "thriller"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="detective",
                attributes={"role": "protagonist", "type": "detective"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="supernatural murders",
                attributes={"theme_type": "investigation", "setting": "victorian"},
            ),
        ],
    ),
    lx.data.ExampleData(
        text="Two friends embark on a road trip adventure across America. The comedy drama explores friendship and self-discovery through humorous situations.",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="comedy drama",
                attributes={"primary_genre": "comedy", "secondary_genre": "drama"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="two friends",
                attributes={"role": "protagonist", "type": "friends"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="friendship and self-discovery",
                attributes={"theme_type": "personal_growth", "setting": "america"},
            ),
        ]
    ),
]
examples_zh = [
    lx.data.ExampleData(
        text="一名太空陆战队员在遥远的星球上与外星生物作战。这部科幻动作电影展示了未来武器和激烈的战斗场面。",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="科幻 动作",
                attributes={"primary_genre": "sci-fi","secondary_genre": "action"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="太空陆战队员",
                attributes={"role": "protagonist","type": "military"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="与外星生物作战",
                attributes={"theme_type": "conflict","setting": "space"},
            ),
        ],
    ),
    lx.data.ExampleData(
        text="一名侦探在维多利亚时代的伦敦调查超自然谋杀案。这部恐怖惊悚片结合了时期剧情和超自然元素。",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="恐怖 惊悚",
                attributes={"primary_genre": "horror", "secondary_genre": "thriller"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="侦探",
                attributes={"role": "protagonist", "type": "detective"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="超自然谋杀案",
                attributes={"theme_type": "investigation", "setting": "victorian"},
            ),
        ],
    ),
    lx.data.ExampleData(
        text="两名朋友开始了一次横跨美国的公路旅行冒险。这部喜剧剧情片通过幽默情境探索友谊和自我发现。",
        extractions=[
            lx.data.Extraction(
                extraction_class="genre",
                extraction_text="喜剧 剧情",
                attributes={"primary_genre": "comedy", "secondary_genre": "drama"},
            ),
            lx.data.Extraction(
                extraction_class="character",
                extraction_text="两名朋友",
                attributes={"role": "protagonist", "type": "friends"},
            ),
            lx.data.Extraction(
                extraction_class="theme",
                extraction_text="友谊和自我发现",
                attributes={"theme_type": "personal_growth", "setting": "america"},
            ),
        ]
    ),
]

config = lx.factory.ModelConfig(
    model_id="qwen-plus",
    provider="LiteLLMLanguageModel",
    provider_kwargs={
        "api_key": "sk-209a240fc995448c9efdbc8eb95eb742",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1"
    }
)

model = lx.factory.create_model(config)

# Extract from each document
extraction_results = []
for doc in sample_documents_zh:
    result = lx.extract(
        text_or_documents=doc,
        prompt_description=prompt_zh,
        examples=examples_zh,
        model=model,  # 传入预配置的模型
        fence_output=False,
        use_schema_constraints=False,
    )
    extraction_results.append(result)
    # i=1
    # jsonl_name = f"{i}-extraction_results.jsonl"
    # lx.io.save_annotated_documents(result, output_name=jsonl_name, output_dir=".")
    #
    # # Generate the visualization from the file
    # html_content = lx.visualize(jsonl_name)
    # html_name = f"{1}-visualization.html"
    # with open(html_name, "w", encoding="utf-8") as f:
    #     if hasattr(html_content, 'data'):
    #         f.write(html_content.data)  # For Jupyter/Colab
    #     else:
    #         f.write(html_content)
    print(f"Successfully extracted from document: {doc[:50]}...")
print(f"Completed tag extraction, processed {len(extraction_results)} documents")



# print("\n3. Processing extraction results and generating vectors...")
#
# processed_data = []
#
# for result in extraction_results:
#     # Generate vectors for documents
#     embedding_response = genai_client.models.embed_content(
#         model=EMBEDDING_MODEL,
#         contents=[result.text],
#         config=EmbedContentConfig(
#             task_type="RETRIEVAL_DOCUMENT",
#             output_dimensionality=EMBEDDING_DIM,
#         ),
#     )
#     embedding = embedding_response.embeddings[0].values
#     print(f"Successfully generated vector: {result.text[:30]}...")
#
#     # Initialize data structure, flatten attributes into separate fields
#     data_entry = {
#         "id": result.document_id or str(uuid.uuid4()),
#         "document_text": result.text,
#         "embedding": embedding,
#         # Initialize all possible fields with default values
#         "genre": "unknown",
#         "primary_genre": "unknown",
#         "secondary_genre": "unknown",
#         "character_role": "unknown",
#         "character_type": "unknown",
#         "theme_type": "unknown",
#         "theme_setting": "unknown",
#     }
#
#     # Process extraction results, flatten attributes
#     for extraction in result.extractions:
#         if extraction.extraction_class == "genre":
#             # Flatten genre attributes
#             data_entry["genre"] = extraction.extraction_text
#             attrs = extraction.attributes or {}
#             data_entry["primary_genre"] = attrs.get("primary_genre", "unknown")
#             data_entry["secondary_genre"] = attrs.get("secondary_genre", "unknown")
#
#         elif extraction.extraction_class == "character":
#             # Flatten character attributes (take first main character's attributes)
#             attrs = extraction.attributes or {}
#             if data_entry["character_role"] == "unknown":  # Only take first character's attributes
#                 data_entry["character_role"] = attrs.get("role", "unknown")
#                 data_entry["character_type"] = attrs.get("type", "unknown")
#
#         elif extraction.extraction_class == "theme":
#             # Flatten theme attributes (take first main theme's attributes)
#             attrs = extraction.attributes or {}
#             if data_entry["theme_type"] == "unknown":  # Only take first theme's attributes
#                 data_entry["theme_type"] = attrs.get("theme_type", "unknown")
#                 data_entry["theme_setting"] = attrs.get("setting", "unknown")
#
#     processed_data.append(data_entry)
#
# print(f"Completed data processing, ready to insert {len(processed_data)} records")
#
#
# print("\n4. Inserting data into Milvus...")
#
# if processed_data:
#     res = client.insert(
#         collection_name=COLLECTION_NAME,
#         data=processed_data
#     )
#     print(f"Successfully inserted {len(processed_data)} documents into Milvus")
#     print(f"Insert result: {res}")
# else:
#     print("No data to insert")
#
#
# print("\n=== Filter Expression Search Examples ===")
#
# # Load collection into memory for querying
# print("Loading collection into memory...")
# client.load_collection(collection_name=COLLECTION_NAME)
# print("Collection loaded successfully")
#
# # Search for thriller movies
# print("\n1. Searching for thriller movies:")
# results = client.query(
#     collection_name=COLLECTION_NAME,
#     filter='secondary_genre == "thriller"',
#     output_fields=["document_text", "genre", "primary_genre", "secondary_genre"],
#     limit=5,
# )
# for result in results:
#     print(f"- {result['document_text'][:100]}...")
#     print(
#         f"  Genre: {result['genre']} ({result.get('primary_genre')}-{result.get('secondary_genre')})"
#     )
#
# # Search for movies with military characters
# print("\n2. Searching for movies with military characters:")
# results = client.query(
#     collection_name=COLLECTION_NAME,
#     filter='character_type == "military"',
#     output_fields=["document_text", "genre", "character_role", "character_type"],
#     limit=5,
# )
# for result in results:
#     print(f"- {result['document_text'][:100]}...")
#     print(f"  Genre: {result['genre']}")
#     print(
#         f"  Character: {result.get('character_role')} ({result.get('character_type')})"
#     )
#
# print("\n=== Semantic Search Examples ===")
#
# # 1. Search for action-related content + only thriller genre
# print("\n1. Searching for action-related content + only thriller genre:")
# query_text = "action fight combat battle explosion"
# query_embedding_response = genai_client.models.embed_content(
#     model=EMBEDDING_MODEL,
#     contents=[query_text],
#     config=EmbedContentConfig(
#         task_type="RETRIEVAL_QUERY",
#         output_dimensionality=EMBEDDING_DIM,
#     ),
# )
# query_embedding = query_embedding_response.embeddings[0].values
#
# results = client.search(
#     collection_name=COLLECTION_NAME,
#     data=[query_embedding],
#     anns_field="embedding",
#     limit=3,
#     filter='secondary_genre == "thriller"',
#     output_fields=["document_text", "genre", "primary_genre", "secondary_genre"],
#     search_params={"metric_type": "COSINE"},
# )
#
# if results:
#     for result in results[0]:
#         print(f"- Similarity: {result['distance']:.4f}")
#         print(f"  Text: {result['document_text'][:100]}...")
#         print(
#             f"  Genre: {result.get('genre')} "
#             f"({result.get('primary_genre')}-{result.get('secondary_genre')})"
#         )
#
# # 2. Search for magic-related content + fantasy genre + conflict theme
# print("\n2. Searching for magic-related content + fantasy genre + conflict theme:")
# query_text = "magic wizard spell fantasy magical"
# query_embedding_response = genai_client.models.embed_content(
#     model=EMBEDDING_MODEL,
#     contents=[query_text],
#     config=EmbedContentConfig(
#         task_type="RETRIEVAL_QUERY",
#         output_dimensionality=EMBEDDING_DIM,
#     ),
# )
# query_embedding = query_embedding_response.embeddings[0].values
#
# results = client.search(
#     collection_name=COLLECTION_NAME,
#     data=[query_embedding],
#     anns_field="embedding",
#     limit=3,
#     filter='primary_genre == "fantasy" and theme_type == "conflict"',
#     output_fields=[
#         "document_text",
#         "genre",
#         "primary_genre",
#         "theme_type",
#         "theme_setting",
#     ],
#     search_params={"metric_type": "COSINE"},
# )
#
# if results:
#     for result in results[0]:
#         print(f"- Similarity: {result['distance']:.4f}")
#         print(f"  Text: {result['document_text'][:100]}...")
#         print(f"  Genre: {result.get('genre')} ({result.get('primary_genre')})")
#         print(f"  Theme: {result.get('theme_type')} ({result.get('theme_setting')})")
#
# print("\n=== Demo Complete ===")

